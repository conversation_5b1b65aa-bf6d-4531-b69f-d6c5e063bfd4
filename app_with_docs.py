# -*- coding: utf-8 -*-
"""
Flask API 主应用 - 带Swagger文档
提供用户信息查询API服务，支持GET和POST请求
"""
import logging
import os
from flask import Flask
from flask_restx import Api, Resource, fields, reqparse
from blueprints.user_api import user_api

def create_app():
    """
    创建Flask应用实例
    Returns:
        Flask: 配置好的Flask应用实例
    """
    # 初始化Flask应用
    app = Flask(__name__)
    
    # 应用基本配置
    app.config['DEBUG'] = False
    app.config['JSON_AS_ASCII'] = False
    app.config['JSONIFY_PRETTYPRINT_REGULAR'] = True
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 配置Flask-RESTX
    api = Api(
        app,
        version='2.0',
        title='AI Out Call API - 支持GET/POST双重请求',
        description='''
        用户信息查询和物件管理API服务
        
        ## 🎯 新功能
        - ✅ 所有接口都支持GET和POST请求
        - ✅ GET请求使用查询参数
        - ✅ POST请求使用JSON请求体
        - ✅ 统一的响应格式
        - ✅ 完整的参数验证
        
        ## 📋 接口列表
        1. **用户信息查询** - `/api/user-info`
        2. **物件ID列表查询** - `/api/get_postid`
        3. **物件详细信息查询** - `/api/get_postinfo`
        4. **物件支付信息查询** - `/api/back/post`
        5. **高级功能物件ID查询** - `/api/advanced/post_id`
        6. **高级功能列表查询** - `/api/advanced/advanced_list`
        7. **高级功能价格查询** - `/api/advanced/back`
        
        ## 🔧 使用方法
        
        ### GET请求示例
        ```
        GET /api/user-info?number=0940875802
        ```
        
        ### POST请求示例
        ```
        POST /api/user-info
        Content-Type: application/json
        
        {
            "number": "0940875802"
        }
        ```
        
        ## 📊 响应格式
        
        ### 成功响应
        ```json
        {
            "is_success": true,
            "data": { ... },
            "message": ""
        }
        ```
        
        ### 错误响应
        ```json
        {
            "is_success": false,
            "data": {},
            "message": "错误描述"
        }
        ```
        ''',
        doc='/docs/',
        prefix='/api'
    )
    
    # 定义通用响应模型
    response_model = api.model('Response', {
        'is_success': fields.Boolean(description='操作是否成功', required=True),
        'data': fields.Raw(description='返回数据'),
        'message': fields.String(description='响应消息')
    })
    
    # 定义请求模型
    user_request_model = api.model('UserRequest', {
        'number': fields.String(description='用户手机号码', required=True, example='0940875802')
    })
    
    post_request_model = api.model('PostRequest', {
        'number': fields.String(description='用户手机号码', required=True, example='0940875802'),
        'post_id': fields.String(description='物件ID', required=True, example='S15799850')
    })
    
    advanced_request_model = api.model('AdvancedRequest', {
        'number': fields.String(description='用户手机号码', required=True, example='0940875802'),
        'post_id': fields.String(description='物件ID', required=True, example='S15799850'),
        'advanced': fields.String(description='高级功能名称', required=True, example='组合置顶')
    })
    
    # 添加命名空间
    ns_user = api.namespace('user', description='用户相关操作')
    ns_post = api.namespace('post', description='物件相关操作')
    ns_advanced = api.namespace('advanced', description='高级功能相关操作')
    
    # 注册原有蓝图（保持兼容性）
    app.register_blueprint(user_api, url_prefix='/api')
    
    # 添加示例接口到文档（展示GET/POST支持）
    @ns_user.route('/info')
    @ns_user.doc('get_user_info_example')
    class UserInfoExample(Resource):
        @ns_user.doc('获取用户信息 (GET)')
        @ns_user.param('number', '用户手机号码', required=True, type='string', _in='query')
        @ns_user.marshal_with(response_model)
        def get(self):
            """
            获取用户信息 (GET请求)
            
            使用查询参数获取用户基础信息
            """
            return {"is_success": True, "data": {}, "message": "请使用 /api/user-info 接口"}
        
        @ns_user.doc('获取用户信息 (POST)')
        @ns_user.expect(user_request_model)
        @ns_user.marshal_with(response_model)
        def post(self):
            """
            获取用户信息 (POST请求)
            
            使用JSON请求体获取用户基础信息
            """
            return {"is_success": True, "data": {}, "message": "请使用 /api/user-info 接口"}
    
    @ns_post.route('/info')
    @ns_post.doc('get_post_info_example')
    class PostInfoExample(Resource):
        @ns_post.doc('获取物件信息 (GET)')
        @ns_post.param('number', '用户手机号码', required=True, type='string', _in='query')
        @ns_post.param('post_id', '物件ID', required=True, type='string', _in='query')
        @ns_post.marshal_with(response_model)
        def get(self):
            """
            获取物件详细信息 (GET请求)
            """
            return {"is_success": True, "data": {}, "message": "请使用 /api/get_postinfo 接口"}
        
        @ns_post.doc('获取物件信息 (POST)')
        @ns_post.expect(post_request_model)
        @ns_post.marshal_with(response_model)
        def post(self):
            """
            获取物件详细信息 (POST请求)
            """
            return {"is_success": True, "data": {}, "message": "请使用 /api/get_postinfo 接口"}
    
    @ns_advanced.route('/price')
    @ns_advanced.doc('get_advanced_price_example')
    class AdvancedPriceExample(Resource):
        @ns_advanced.doc('获取高级功能价格 (GET)')
        @ns_advanced.param('number', '用户手机号码', required=True, type='string', _in='query')
        @ns_advanced.param('post_id', '物件ID', required=True, type='string', _in='query')
        @ns_advanced.param('advanced', '高级功能名称', required=True, type='string', _in='query')
        @ns_advanced.marshal_with(response_model)
        def get(self):
            """
            获取高级功能价格 (GET请求)
            """
            return {"is_success": True, "data": {}, "message": "请使用 /api/advanced/back 接口"}
        
        @ns_advanced.doc('获取高级功能价格 (POST)')
        @ns_advanced.expect(advanced_request_model)
        @ns_advanced.marshal_with(response_model)
        def post(self):
            """
            获取高级功能价格 (POST请求)
            """
            return {"is_success": True, "data": {}, "message": "请使用 /api/advanced/back 接口"}
    
    # 健康检查路由
    @app.route('/')
    def health_check():
        """健康检查接口"""
        return {
            "service": "AI Out Call API",
            "status": "running",
            "version": "2.0",
            "features": [
                "GET/POST双重支持",
                "统一响应格式", 
                "完整参数验证",
                "Swagger文档"
            ],
            "docs": "/docs/",
            "endpoints": {
                "用户信息": "/api/user-info",
                "物件ID列表": "/api/get_postid",
                "物件详细信息": "/api/get_postinfo",
                "物件支付信息": "/api/back/post",
                "高级功能物件ID": "/api/advanced/post_id",
                "高级功能列表": "/api/advanced/advanced_list",
                "高级功能价格": "/api/advanced/back"
            }
        }
    
    return app

# 创建应用实例
app = create_app()

if __name__ == '__main__':
    print("🚀 启动AI Out Call API服务...")
    print("📍 健康检查: http://localhost:8550/")
    print("📍 API文档: http://localhost:8550/docs/")
    print("📍 用户信息API: http://localhost:8550/api/user-info?number=0940875802")
    print("🌐 支持GET和POST双重请求")
    
    app.run(
        host='0.0.0.0',
        port=8550,
        debug=False
    )
