"""
用户API蓝图模块
提供用户信息查询接口
"""
import json
import os
import logging
from flask import Blueprint, request, jsonify

# 创建蓝图对象
user_api = Blueprint('user_api', __name__)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_users_data():
    """
    加载用户数据
    Returns:
        dict: 用户数据字典，如果加载失败返回空字典
    """
    try:
        # 获取数据文件路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        data_file = os.path.join(os.path.dirname(current_dir), 'data', 'users.json')

        with open(data_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error(f"用户数据文件未找到: {data_file}")
        return {}
    except json.JSONDecodeError:
        logger.error("用户数据文件格式错误")
        return {}
    except Exception as e:
        logger.error(f"加载用户数据时发生错误: {str(e)}")
        return {}

def load_posts_data():
    """
    加载物件数据
    Returns:
        dict: 物件数据字典，如果加载失败返回空字典
    """
    try:
        # 获取数据文件路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        data_file = os.path.join(os.path.dirname(current_dir), 'data', 'posts.json')

        with open(data_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error(f"物件数据文件未找到: {data_file}")
        return {}
    except json.JSONDecodeError:
        logger.error("物件数据文件格式错误")
        return {}
    except Exception as e:
        logger.error(f"加载物件数据时发生错误: {str(e)}")
        return {}

def load_advanced_data():
    """
    加载高级功能数据
    Returns:
        dict: 高级功能数据字典，如果加载失败返回空字典
    """
    try:
        # 获取数据文件路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        data_file = os.path.join(os.path.dirname(current_dir), 'data', 'advanced.json')

        with open(data_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error(f"高级功能数据文件未找到: {data_file}")
        return {}
    except json.JSONDecodeError:
        logger.error("高级功能数据文件格式错误")
        return {}
    except Exception as e:
        logger.error(f"加载高级功能数据时发生错误: {str(e)}")
        return {}

def create_response(is_success, data=None, message=""):
    """
    创建统一的响应格式
    Args:
        is_success (bool): 操作是否成功
        data (dict): 返回的数据，默认为空字典
        message (str): 响应消息
    Returns:
        dict: 统一格式的响应
    """
    if data is None:
        data = {}

    return {
        "is_success": is_success,
        "rel": data,
        "message": message
    }

def get_request_params(*param_names):
    """
    统一获取请求参数，支持GET和POST请求
    Args:
        *param_names: 需要获取的参数名列表
    Returns:
        dict: 参数字典，如果参数不存在则值为None
    """
    params = {}

    if request.method == 'GET':
        # GET请求从查询参数获取
        for param_name in param_names:
            params[param_name] = request.args.get(param_name)
    elif request.method == 'POST':
        # POST请求从JSON请求体获取
        if request.is_json:
            data = request.get_json() or {}
            for param_name in param_names:
                params[param_name] = data.get(param_name)
        else:
            # 如果不是JSON格式，返回空参数
            for param_name in param_names:
                params[param_name] = None
    else:
        # 其他请求方法，返回空参数
        for param_name in param_names:
            params[param_name] = None

    return params

@user_api.route('/user-info', methods=['GET', 'POST'])
def get_user_info():
    """
    获取用户信息接口
    支持GET和POST请求

    GET请求 - Query Parameters:
        number (str): 用户手机号码

    POST请求 - Request Body (JSON):
        {
            "number": "用户手机号码"
        }

    Returns:
        JSON: 用户信息或错误信息
    """
    try:
        # 统一获取请求参数
        params = get_request_params('number')
        number = params['number']

        # 参数验证
        if not number:
            logger.warning(f"请求缺少number参数 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：number不能为空"
            )), 400

        if not str(number).strip():
            logger.warning(f"请求number参数为空字符串 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：number不能为空"
            )), 400
        
        # 加载用户数据
        users_data = load_users_data()
        
        if not users_data:
            logger.error("无法加载用户数据")
            return jsonify(create_response(
                is_success=False,
                message="服务器内部错误"
            )), 500
        
        # 查询用户信息
        user_info = users_data.get(str(number))

        if user_info:
            logger.info(f"成功查询用户信息: {number} - 方法: {request.method}")
            return jsonify(create_response(
                is_success=True,
                data=user_info
            )), 200
        else:
            logger.info(f"用户不存在: {number} - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="用户不存在"
            )), 404

    except Exception as e:
        logger.error(f"处理用户信息查询时发生错误: {str(e)} - 方法: {request.method}")
        return jsonify(create_response(
            is_success=False,
            message="服务器内部错误"
        )), 500

@user_api.route('/get_postid', methods=['GET', 'POST'])
def get_post_ids():
    """
    获取用户物件ID列表接口
    支持GET和POST请求

    GET请求 - Query Parameters:
        number (str): 用户手机号码

    POST请求 - Request Body (JSON):
        {
            "number": "用户手机号码"
        }

    Returns:
        JSON: 物件ID列表或错误信息
    """
    try:
        # 统一获取请求参数
        params = get_request_params('number')
        number = params['number']

        # 参数验证
        if not number:
            logger.warning(f"请求缺少number参数 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：number不能为空"
            )), 400

        if not str(number).strip():
            logger.warning(f"请求number参数为空字符串 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：number不能为空"
            )), 400

        # 加载物件数据
        posts_data = load_posts_data()

        if not posts_data:
            logger.error("无法加载物件数据")
            return jsonify(create_response(
                is_success=False,
                message="服务器内部错误"
            )), 500

        # 查询用户物件
        user_posts = posts_data.get(str(number), {})

        if user_posts:
            # 获取物件ID列表
            post_ids = list(user_posts.keys())
            post_ids_str = "，".join(post_ids)
            count = len(post_ids)

            logger.info(f"成功查询用户物件: {number}, 共{count}笔 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=True,
                data=post_ids_str,
                message=f"共查询到{count}笔可取消的物件"
            )), 200
        else:
            logger.info(f"用户无物件: {number} - 方法: {request.method}")
            return jsonify(create_response(
                is_success=True,
                data="",
                message="无符合取消条件的物件"
            )), 200

    except Exception as e:
        logger.error(f"处理物件ID查询时发生错误: {str(e)} - 方法: {request.method}")
        return jsonify(create_response(
            is_success=False,
            message="服务器内部错误"
        )), 500

@user_api.route('/get_postinfo', methods=['GET', 'POST'])
def get_post_info():
    """
    获取物件详细信息接口
    支持GET和POST请求

    GET请求 - Query Parameters:
        number (str): 用户手机号码
        post_id (str): 物件ID

    POST请求 - Request Body (JSON):
        {
            "number": "用户手机号码",
            "post_id": "物件ID"
        }

    Returns:
        JSON: 物件详细信息或错误信息
    """
    try:
        # 统一获取请求参数
        params = get_request_params('number', 'post_id')
        number = params['number']
        post_id = params['post_id']

        # 参数验证
        if not number:
            logger.warning(f"请求缺少number参数 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：number不能为空"
            )), 400

        if not str(number).strip():
            logger.warning(f"请求number参数为空字符串 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：number不能为空"
            )), 400

        if not post_id:
            logger.warning(f"请求缺少post_id参数 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：post_id不能为空"
            )), 400

        if not str(post_id).strip():
            logger.warning(f"请求post_id参数为空字符串 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：post_id不能为空"
            )), 400

        # 加载物件数据
        posts_data = load_posts_data()

        if not posts_data:
            logger.error("无法加载物件数据")
            return jsonify(create_response(
                is_success=False,
                message="服务器内部错误"
            )), 500

        # 查询用户物件
        user_posts = posts_data.get(str(number), {})

        if not user_posts:
            logger.info(f"用户无物件: {number} - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="用户无物件信息"
            )), 404

        # 查询特定物件信息
        post_info = user_posts.get(str(post_id))

        if post_info:
            logger.info(f"成功查询物件信息: {number} - {post_id} - 方法: {request.method}")
            return jsonify(create_response(
                is_success=True,
                data=post_info
            )), 200
        else:
            logger.info(f"物件不存在: {number} - {post_id} - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="物件不存在"
            )), 404

    except Exception as e:
        logger.error(f"处理物件信息查询时发生错误: {str(e)} - 方法: {request.method}")
        return jsonify(create_response(
            is_success=False,
            message="服务器内部错误"
        )), 500

@user_api.route('/back/post', methods=['GET', 'POST'])
def get_post_pay():
    """
    获取物件支付信息接口
    支持GET和POST请求

    GET请求 - Query Parameters:
        number (str): 用户手机号码
        post_id (str): 物件ID

    POST请求 - Request Body (JSON):
        {
            "number": "用户手机号码",
            "post_id": "物件ID"
        }

    Returns:
        JSON: 物件支付信息或错误信息
    """
    try:
        # 统一获取请求参数
        params = get_request_params('number', 'post_id')
        number = params['number']
        post_id = params['post_id']

        # 参数验证
        if not number:
            logger.warning(f"请求缺少number参数 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：number不能为空"
            )), 400

        if not str(number).strip():
            logger.warning(f"请求number参数为空字符串 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：number不能为空"
            )), 400

        if not post_id:
            logger.warning(f"请求缺少post_id参数 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：post_id不能为空"
            )), 400

        if not str(post_id).strip():
            logger.warning(f"请求post_id参数为空字符串 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：post_id不能为空"
            )), 400

        # 加载物件数据
        posts_data = load_posts_data()

        if not posts_data:
            logger.error("无法加载物件数据")
            return jsonify(create_response(
                is_success=False,
                message="服务器内部错误"
            )), 500

        # 查询用户物件
        user_posts = posts_data.get(str(number), {})

        if not user_posts:
            logger.info(f"用户无物件: {number}")
            return jsonify(create_response(
                is_success=False,
                message="用户无物件信息"
            )), 404

        # 查询特定物件信息
        post_info = user_posts.get(str(post_id))

        if post_info and 'pay' in post_info:
            pay_info = post_info['pay']
            logger.info(f"成功查询物件支付信息: {number} - {post_id} - 方法: {request.method}")
            return jsonify(create_response(
                is_success=True,
                data=pay_info
            )), 200
        else:
            logger.info(f"物件不存在或无支付信息: {number} - {post_id} - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="物件不存在或无支付信息"
            )), 404

    except Exception as e:
        logger.error(f"处理物件支付信息查询时发生错误: {str(e)} - 方法: {request.method}")
        return jsonify(create_response(
            is_success=False,
            message="服务器内部错误"
        )), 500

@user_api.route('/advanced/post_id', methods=['GET', 'POST'])
def get_advanced_post_ids():
    """
    获取用户高级功能物件ID列表接口
    支持GET和POST请求

    GET请求 - Query Parameters:
        number (str): 用户手机号码

    POST请求 - Request Body (JSON):
        {
            "number": "用户手机号码"
        }

    Returns:
        JSON: 高级功能物件ID列表或错误信息
    """
    try:
        # 统一获取请求参数
        params = get_request_params('number')
        number = params['number']

        # 参数验证
        if not number:
            logger.warning(f"请求缺少number参数 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：number不能为空"
            )), 400

        if not str(number).strip():
            logger.warning(f"请求number参数为空字符串 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：number不能为空"
            )), 400

        # 加载高级功能数据
        advanced_data = load_advanced_data()

        if not advanced_data:
            logger.error("无法加载高级功能数据")
            return jsonify(create_response(
                is_success=False,
                message="服务器内部错误"
            )), 500

        # 查询用户高级功能物件
        user_advanced = advanced_data.get(str(number), {})

        if user_advanced:
            # 获取物件ID列表
            post_ids = list(user_advanced.keys())
            post_ids_str = "，".join(post_ids)
            count = len(post_ids)

            logger.info(f"成功查询用户高级功能物件: {number}, 共{count}笔 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=True,
                data=post_ids_str,
                message=f"共查询到{count}笔高级功能物件"
            )), 200
        else:
            logger.info(f"用户无高级功能物件: {number} - 方法: {request.method}")
            return jsonify(create_response(
                is_success=True,
                data="",
                message="无高级功能物件"
            )), 200

    except Exception as e:
        logger.error(f"处理高级功能物件ID查询时发生错误: {str(e)} - 方法: {request.method}")
        return jsonify(create_response(
            is_success=False,
            message="服务器内部错误"
        )), 500

@user_api.route('/advanced/advanced_list', methods=['GET', 'POST'])
def get_advanced_list():
    """
    获取物件高级功能列表接口
    支持GET和POST请求

    GET请求 - Query Parameters:
        number (str): 用户手机号码
        post_id (str): 物件ID

    POST请求 - Request Body (JSON):
        {
            "number": "用户手机号码",
            "post_id": "物件ID"
        }

    Returns:
        JSON: 高级功能列表或错误信息
    """
    try:
        # 统一获取请求参数
        params = get_request_params('number', 'post_id')
        number = params['number']
        post_id = params['post_id']

        # 参数验证
        if not number:
            logger.warning(f"请求缺少number参数 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：number不能为空"
            )), 400

        if not str(number).strip():
            logger.warning(f"请求number参数为空字符串 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：number不能为空"
            )), 400

        if not post_id:
            logger.warning(f"请求缺少post_id参数 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：post_id不能为空"
            )), 400

        if not str(post_id).strip():
            logger.warning(f"请求post_id参数为空字符串 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：post_id不能为空"
            )), 400

        # 加载高级功能数据
        advanced_data = load_advanced_data()

        if not advanced_data:
            logger.error("无法加载高级功能数据")
            return jsonify(create_response(
                is_success=False,
                message="服务器内部错误"
            )), 500

        # 查询用户高级功能物件
        user_advanced = advanced_data.get(str(number), {})

        if not user_advanced:
            logger.info(f"用户无高级功能物件: {number} - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="用户无高级功能物件"
            )), 404

        # 查询特定物件的高级功能
        post_advanced = user_advanced.get(str(post_id), {})

        if not post_advanced:
            logger.info(f"物件不存在: {number} - {post_id} - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="物件不存在"
            )), 404

        # 获取高级功能列表
        advanced_features = post_advanced.get('advanced', {})

        if advanced_features:
            # 获取高级功能键列表
            feature_keys = list(advanced_features.keys())
            features_str = "，".join(feature_keys)
            count = len(feature_keys)

            logger.info(f"成功查询物件高级功能: {number} - {post_id}, 共{count}个功能 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=True,
                data=features_str,
                message=f"共查询到{count}个高级功能"
            )), 200
        else:
            logger.info(f"物件无高级功能: {number} - {post_id} - 方法: {request.method}")
            return jsonify(create_response(
                is_success=True,
                data="",
                message="物件无高级功能"
            )), 200

    except Exception as e:
        logger.error(f"处理高级功能列表查询时发生错误: {str(e)} - 方法: {request.method}")
        return jsonify(create_response(
            is_success=False,
            message="服务器内部错误"
        )), 500

@user_api.route('/advanced/back', methods=['GET', 'POST'])
def get_advanced_price():
    """
    获取高级功能价格信息接口
    支持GET和POST请求

    GET请求 - Query Parameters:
        number (str): 用户手机号码
        post_id (str): 物件ID
        advanced (str): 高级功能名称

    POST请求 - Request Body (JSON):
        {
            "number": "用户手机号码",
            "post_id": "物件ID",
            "advanced": "高级功能名称"
        }

    Returns:
        JSON: 高级功能价格信息或错误信息
    """
    try:
        # 统一获取请求参数
        params = get_request_params('number', 'post_id', 'advanced')
        number = params['number']
        post_id = params['post_id']
        advanced_name = params['advanced']

        # 参数验证
        if not number:
            logger.warning(f"请求缺少number参数 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：number不能为空"
            )), 400

        if not str(number).strip():
            logger.warning(f"请求number参数为空字符串 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：number不能为空"
            )), 400

        if not post_id:
            logger.warning(f"请求缺少post_id参数 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：post_id不能为空"
            )), 400

        if not str(post_id).strip():
            logger.warning(f"请求post_id参数为空字符串 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：post_id不能为空"
            )), 400

        if not advanced_name:
            logger.warning(f"请求缺少advanced参数 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：advanced不能为空"
            )), 400

        if not str(advanced_name).strip():
            logger.warning(f"请求advanced参数为空字符串 - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="参数错误：advanced不能为空"
            )), 400

        # 加载高级功能数据
        advanced_data = load_advanced_data()

        if not advanced_data:
            logger.error("无法加载高级功能数据")
            return jsonify(create_response(
                is_success=False,
                message="服务器内部错误"
            )), 500

        # 查询用户高级功能物件
        user_advanced = advanced_data.get(str(number), {})

        if not user_advanced:
            logger.info(f"用户无高级功能物件: {number}")
            return jsonify(create_response(
                is_success=False,
                message="用户无高级功能物件"
            )), 404

        # 查询特定物件信息
        post_info = user_advanced.get(str(post_id))

        if not post_info:
            logger.info(f"物件不存在: {number} - {post_id}")
            return jsonify(create_response(
                is_success=False,
                message="物件不存在"
            )), 404

        # 查询高级功能价格
        advanced_features = post_info.get('advanced', {})
        price = advanced_features.get(str(advanced_name))

        if price:
            logger.info(f"成功查询高级功能价格: {number} - {post_id} - {advanced_name} - 方法: {request.method}")
            return jsonify(create_response(
                is_success=True,
                data=price
            )), 200
        else:
            logger.info(f"高级功能不存在: {number} - {post_id} - {advanced_name} - 方法: {request.method}")
            return jsonify(create_response(
                is_success=False,
                message="高级功能不存在"
            )), 404

    except Exception as e:
        logger.error(f"处理高级功能价格查询时发生错误: {str(e)} - 方法: {request.method}")
        return jsonify(create_response(
            is_success=False,
            message="服务器内部错误"
        )), 500
